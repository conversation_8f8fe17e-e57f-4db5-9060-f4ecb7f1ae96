<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ user.name }} - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://www.w3schools.com/w3css/4/w3.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #121212;
            color: #e0e0e0;
        }

        .w3-dark-grey {
            background-color: #1e1e1e !important;
            color: #e0e0e0 !important;
        }

        .custom-purple {
            background-color: #bb86fc !important;
            color: #000 !important;
        }

        .custom-dark-purple {
            background-color: #6200ea !important;
        }

        .text-purple {
            color: #bb86fc !important;
        }

        .skill-tag {
            display: inline-block;
            margin: 2px;
        }

        .project-card {
            margin-bottom: 16px;
            background-color: #2d2d2d !important;
            border: 1px solid #444 !important;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 16px 0;
        }
    </style>
</head>
<body class="w3-black">
    <div class="w3-container w3-margin-top">
        <div class="w3-card-4 w3-dark-grey w3-margin-bottom" style="max-width: 900px; margin: 0 auto;">

            <!-- Header -->
            <header class="w3-container custom-purple w3-center w3-padding-32">
                <h1 class="w3-jumbo w3-margin-bottom">{{ user.name }}</h1>
                <div class="w3-row w3-margin-top">
                    {% if user.email %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom">
                        <i class="fa fa-envelope w3-margin-right"></i>{{ user.email }}
                    </div>
                    {% endif %}
                    {% if user.location %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom">
                        <i class="fa fa-map-marker w3-margin-right"></i>{{ user.location }}
                    </div>
                    {% endif %}
                    {% if user.blog %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom">
                        <i class="fa fa-globe w3-margin-right"></i>{{ user.blog }}
                    </div>
                    {% endif %}
                    {% if user.company %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom">
                        <i class="fa fa-building w3-margin-right"></i>{{ user.company }}
                    </div>
                    {% endif %}
                </div>
            </header>

            <!-- Professional Summary -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-purple w3-border-bottom w3-border-purple w3-padding-bottom">
                    <i class="fa fa-user w3-margin-right"></i>Perfil Profissional
                </h2>
                <div class="w3-panel w3-grey w3-leftbar w3-border-purple">
                    <p class="w3-large">{{ professional_summary }}</p>
                </div>
            </div>

            <!-- Technical Skills -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-purple w3-border-bottom w3-border-purple w3-padding-bottom">
                    <i class="fa fa-cogs w3-margin-right"></i>Habilidades Técnicas
                </h2>
                <div class="w3-row">
                    {% set all_skills = [] %}
                    {% for repo in repositories %}
                      {% for skill in repo.llm_skills %}
                        {% if skill not in all_skills %}
                          {% set _ = all_skills.append(skill) %}
                        {% endif %}
                      {% endfor %}
                    {% endfor %}

                    {% for skill in all_skills[:15] %}
                    <span class="w3-tag custom-purple w3-margin-right w3-margin-bottom skill-tag">{{ skill }}</span>
                    {% endfor %}
                </div>
            </div>

            <!-- Featured Projects -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-purple w3-border-bottom w3-border-purple w3-padding-bottom">
                    <i class="fa fa-rocket w3-margin-right"></i>Projetos em Destaque
                </h2>

                {% for repo in repositories[:8] %}
                <div class="w3-card project-card">
                    <div class="w3-container w3-padding">
                        <h3 class="text-purple">{{ repo.llm_title }}</h3>
                        {% if repo.llm_description %}
                        <p>{{ repo.llm_description }}</p>
                        {% endif %}

                        <div class="w3-margin-top">
                            <strong>Tecnologias:</strong>
                            {% for skill in repo.llm_skills %}
                            <span class="w3-tag w3-purple w3-small w3-margin-right">{{ skill }}</span>
                            {% endfor %}
                        </div>

                        <div class="w3-row w3-margin-top">
                            {% if repo.stars > 0 %}
                            <div class="w3-col s6 m4">
                                <i class="fa fa-star w3-text-yellow"></i> {{ repo.stars }} Stars
                            </div>
                            {% endif %}
                            {% if repo.forks > 0 %}
                            <div class="w3-col s6 m4">
                                <i class="fa fa-code-fork w3-text-green"></i> {{ repo.forks }} Forks
                            </div>
                            {% endif %}
                        </div>

                        {% if repo.llm_achievements %}
                        <div class="w3-margin-top">
                            <strong>Conquistas:</strong>
                            <ul class="w3-ul">
                                {% for achievement in repo.llm_achievements %}
                                <li>{{ achievement }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <a href="{{ repo.url }}" class="w3-button custom-purple w3-margin-top" target="_blank">
                            <i class="fa fa-external-link"></i> Ver Projeto
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            <!-- GitHub Statistics -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-purple w3-border-bottom w3-border-purple w3-padding-bottom">
                    <i class="fa fa-bar-chart w3-margin-right"></i>Estatísticas GitHub
                </h2>

                <div class="stats-grid">
                    <div class="w3-card w3-center w3-padding custom-dark-purple w3-text-white">
                        <h3>{{ stats.total_repos }}</h3>
                        <p>Repositórios</p>
                    </div>
                    <div class="w3-card w3-center w3-padding custom-dark-purple w3-text-white">
                        <h3>{{ stats.total_stars }}</h3>
                        <p>Total Stars</p>
                    </div>
                    <div class="w3-card w3-center w3-padding custom-dark-purple w3-text-white">
                        <h3>{{ stats.total_forks }}</h3>
                        <p>Total Forks</p>
                    </div>
                    <div class="w3-card w3-center w3-padding custom-dark-purple w3-text-white">
                        <h3>{{ stats.followers }}</h3>
                        <p>Seguidores</p>
                    </div>
                </div>

                <h3 class="text-purple w3-margin-top">
                    <i class="fa fa-code w3-margin-right"></i>Linguagens Mais Utilizadas
                </h3>
                <div class="w3-row">
                    {% for language, count in stats.languages.items() %}
                    <span class="w3-tag w3-teal w3-margin-right w3-margin-bottom">{{ language }} ({{ count }})</span>
                    {% endfor %}
                </div>
            </div>

            {% if gamified %}
            <!-- Gamification Section -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-purple w3-border-bottom w3-border-purple w3-padding-bottom">
                    <i class="fa fa-trophy w3-margin-right"></i>Conquistas de Desenvolvedor
                </h2>

                {% set total_points = (stats.total_stars * 10) + (stats.total_forks * 5) + (stats.total_repos * 2) + (stats.languages|length * 15) %}

                <div class="w3-panel w3-grey w3-center w3-padding-large w3-border w3-border-purple">
                    <h3 class="text-purple"><i class="fa fa-target"></i> {{ total_points }} pontos</h3>

                    <h4 class="text-purple">
                        {% if total_points >= 1000 %}
                        <i class="fa fa-star"></i> Ninja Master
                        {% elif total_points >= 500 %}
                        <i class="fa fa-bolt"></i> Code Warrior
                        {% elif total_points >= 200 %}
                        <i class="fa fa-laptop"></i> Script Kiddie
                        {% else %}
                        <i class="fa fa-leaf"></i> Padawan Developer
                        {% endif %}
                    </h4>
                </div>

                <div class="w3-row-padding w3-margin-top">
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card w3-center w3-padding w3-grey">
                            <i class="fa fa-star w3-text-yellow w3-large"></i>
                            <h4>Star Collector</h4>
                            <p>{{ stats.total_stars }} stars</p>
                        </div>
                    </div>
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card w3-center w3-padding w3-grey">
                            <i class="fa fa-code-fork w3-text-green w3-large"></i>
                            <h4>Fork Master</h4>
                            <p>{{ stats.total_forks }} forks</p>
                        </div>
                    </div>
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card w3-center w3-padding w3-grey">
                            <i class="fa fa-book text-purple w3-large"></i>
                            <h4>Polyglot</h4>
                            <p>{{ stats.languages|length }} linguagens</p>
                        </div>
                    </div>
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card w3-center w3-padding w3-grey">
                            <i class="fa fa-rocket w3-text-red w3-large"></i>
                            <h4>Project Creator</h4>
                            <p>{{ stats.total_repos }} repositórios</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Footer -->
            <footer class="w3-container w3-center w3-padding-32 w3-grey">
                <p class="w3-text-light-grey">
                    <i class="fa fa-code"></i> Currículo gerado automaticamente pelo GitHub ATS Resume Generator
                </p>
            </footer>
        </div>
    </div>
</body>
</html>
