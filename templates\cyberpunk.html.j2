<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ user.name }} - <PERSON><PERSON><PERSON><PERSON><PERSON>lo Cyberpunk</title>
    <link rel="stylesheet" href="https://www.w3schools.com/w3css/4/w3.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Orbitron', monospace;
            background: #0a0a0a;
            color: #00ff41;
            background-image:
                radial-gradient(circle at 25% 25%, #ff00ff 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #00ffff 0%, transparent 50%);
            background-size: 100px 100px;
            animation: backgroundShift 10s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { background-position: 0% 0%; }
            100% { background-position: 100% 100%; }
        }

        .cyber-container {
            background: rgba(0, 0, 0, 0.9) !important;
            border: 2px solid #00ff41 !important;
            box-shadow: 0 0 50px rgba(0, 255, 65, 0.3) !important;
        }

        .cyber-header {
            background: linear-gradient(45deg, #ff00ff, #00ffff) !important;
            color: #000 !important;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .cyber-green {
            background-color: #00ff41 !important;
            color: #000 !important;
        }

        .cyber-pink {
            background-color: #ff00ff !important;
            color: #fff !important;
        }

        .cyber-cyan {
            background-color: #00ffff !important;
            color: #000 !important;
        }

        .text-cyber-green {
            color: #00ff41 !important;
        }

        .text-cyber-pink {
            color: #ff00ff !important;
        }

        .text-cyber-cyan {
            color: #00ffff !important;
        }

        .cyber-card {
            background: rgba(0, 0, 0, 0.8) !important;
            border: 1px solid #00ff41 !important;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.2) !important;
        }

        .cyber-glow {
            text-shadow: 0 0 10px currentColor;
        }

        .skill-tag {
            display: inline-block;
            margin: 2px;
        }

        .project-card {
            margin-bottom: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 16px 0;
        }
        @keyframes glow {
            from { text-shadow: 0 0 10px currentColor; }
            to { text-shadow: 0 0 20px currentColor; }
        }
    </style>
</head>
<body>
    <div class="w3-container w3-margin-top">
        <div class="w3-card-4 cyber-container w3-margin-bottom" style="max-width: 900px; margin: 0 auto;">

            <!-- Header -->
            <header class="w3-container cyber-header w3-center w3-padding-32">
                <h1 class="w3-jumbo w3-margin-bottom cyber-glow" style="animation: glow 2s ease-in-out infinite alternate;">{{ user.name }}</h1>
                <div class="w3-row w3-margin-top">
                    {% if user.email %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom text-cyber-cyan">
                        <i class="fa fa-envelope w3-margin-right"></i>{{ user.email }}
                    </div>
                    {% endif %}
                    {% if user.location %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom text-cyber-cyan">
                        <i class="fa fa-map-marker w3-margin-right"></i>{{ user.location }}
                    </div>
                    {% endif %}
                    {% if user.blog %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom text-cyber-cyan">
                        <i class="fa fa-globe w3-margin-right"></i>{{ user.blog }}
                    </div>
                    {% endif %}
                    {% if user.company %}
                    <div class="w3-col s12 m6 l3 w3-margin-bottom text-cyber-cyan">
                        <i class="fa fa-building w3-margin-right"></i>{{ user.company }}
                    </div>
                    {% endif %}
                </div>
            </header>

            <!-- Professional Summary -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-cyber-pink w3-border-bottom w3-border-pink w3-padding-bottom cyber-glow">
                    <i class="fa fa-user w3-margin-right"></i>PERFIL PROFISSIONAL
                </h2>
                <div class="w3-panel cyber-card w3-leftbar w3-border-pink">
                    <p class="w3-large">{{ professional_summary }}</p>
                </div>
            </div>

            <!-- Technical Skills -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-cyber-pink w3-border-bottom w3-border-pink w3-padding-bottom cyber-glow">
                    <i class="fa fa-cogs w3-margin-right"></i>HABILIDADES TÉCNICAS
                </h2>
                <div class="w3-row">
                    {% set all_skills = [] %}
                    {% for repo in repositories %}
                      {% for skill in repo.llm_skills %}
                        {% if skill not in all_skills %}
                          {% set _ = all_skills.append(skill) %}
                        {% endif %}
                      {% endfor %}
                    {% endfor %}

                    {% for skill in all_skills[:15] %}
                    <span class="w3-tag cyber-pink w3-margin-right w3-margin-bottom skill-tag" style="text-transform: uppercase; letter-spacing: 1px;">{{ skill }}</span>
                    {% endfor %}
                </div>
            </div>

            <!-- Featured Projects -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-cyber-pink w3-border-bottom w3-border-pink w3-padding-bottom cyber-glow">
                    <i class="fa fa-rocket w3-margin-right"></i>PROJETOS EM DESTAQUE
                </h2>

                {% for repo in repositories[:8] %}
                <div class="w3-card cyber-card project-card">
                    <div class="w3-container w3-padding">
                        <h3 class="text-cyber-green cyber-glow">{{ repo.llm_title }}</h3>
                        {% if repo.llm_description %}
                        <p>{{ repo.llm_description }}</p>
                        {% endif %}

                        <div class="w3-margin-top">
                            <strong class="text-cyber-cyan">TECNOLOGIAS:</strong>
                            {% for skill in repo.llm_skills %}
                            <span class="w3-tag cyber-cyan w3-small w3-margin-right" style="text-transform: uppercase;">{{ skill }}</span>
                            {% endfor %}
                        </div>

                        <div class="w3-row w3-margin-top">
                            {% if repo.stars > 0 %}
                            <div class="w3-col s6 m4 text-cyber-green">
                                <i class="fa fa-star"></i> {{ repo.stars }} STARS
                            </div>
                            {% endif %}
                            {% if repo.forks > 0 %}
                            <div class="w3-col s6 m4 text-cyber-cyan">
                                <i class="fa fa-code-fork"></i> {{ repo.forks }} FORKS
                            </div>
                            {% endif %}
                        </div>

                        {% if repo.llm_achievements %}
                        <div class="w3-margin-top">
                            <strong class="text-cyber-pink">CONQUISTAS:</strong>
                            <ul class="w3-ul">
                                {% for achievement in repo.llm_achievements %}
                                <li>{{ achievement }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <a href="{{ repo.url }}" class="w3-button cyber-green w3-margin-top" target="_blank" style="text-transform: uppercase; letter-spacing: 1px;">
                            <i class="fa fa-external-link"></i> ACESSAR
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            <!-- GitHub Statistics -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-cyber-pink w3-border-bottom w3-border-pink w3-padding-bottom cyber-glow">
                    <i class="fa fa-bar-chart w3-margin-right"></i>ESTATÍSTICAS GITHUB
                </h2>

                <div class="stats-grid">
                    <div class="w3-card cyber-card w3-center w3-padding">
                        <h3 class="text-cyber-pink cyber-glow" style="font-size: 2.5em; font-weight: 900;">{{ stats.total_repos }}</h3>
                        <p class="text-cyber-green" style="text-transform: uppercase; letter-spacing: 1px;">REPOS</p>
                    </div>
                    <div class="w3-card cyber-card w3-center w3-padding">
                        <h3 class="text-cyber-pink cyber-glow" style="font-size: 2.5em; font-weight: 900;">{{ stats.total_stars }}</h3>
                        <p class="text-cyber-green" style="text-transform: uppercase; letter-spacing: 1px;">STARS</p>
                    </div>
                    <div class="w3-card cyber-card w3-center w3-padding">
                        <h3 class="text-cyber-pink cyber-glow" style="font-size: 2.5em; font-weight: 900;">{{ stats.total_forks }}</h3>
                        <p class="text-cyber-green" style="text-transform: uppercase; letter-spacing: 1px;">FORKS</p>
                    </div>
                    <div class="w3-card cyber-card w3-center w3-padding">
                        <h3 class="text-cyber-pink cyber-glow" style="font-size: 2.5em; font-weight: 900;">{{ stats.followers }}</h3>
                        <p class="text-cyber-green" style="text-transform: uppercase; letter-spacing: 1px;">FOLLOWERS</p>
                    </div>
                </div>

                <h3 class="text-cyber-pink w3-margin-top cyber-glow">
                    <i class="fa fa-code w3-margin-right"></i>LINGUAGENS
                </h3>
                <div class="w3-row">
                    {% for language, count in stats.languages.items() %}
                    <span class="w3-tag cyber-green w3-margin-right w3-margin-bottom" style="text-transform: uppercase; letter-spacing: 1px; font-weight: 700;">{{ language }} ({{ count }})</span>
                    {% endfor %}
                </div>
            </div>

            {% if gamified %}
            <!-- Gamification Section -->
            <div class="w3-container w3-padding-32">
                <h2 class="text-cyber-pink w3-border-bottom w3-border-pink w3-padding-bottom cyber-glow">
                    <i class="fa fa-trophy w3-margin-right"></i>CONQUISTAS CYBERPUNK
                </h2>

                {% set total_points = (stats.total_stars * 10) + (stats.total_forks * 5) + (stats.total_repos * 2) + (stats.languages|length * 15) %}

                <div class="w3-panel cyber-card w3-center w3-padding-large w3-border w3-border-pink">
                    <h3 class="text-cyber-green cyber-glow" style="font-size: 2em; font-weight: 900;">
                        <i class="fa fa-target"></i> {{ total_points }} PONTOS
                    </h3>

                    <h4 class="text-cyber-green cyber-glow" style="font-size: 2em; font-weight: 900;">
                        {% if total_points >= 1000 %}
                        <i class="fa fa-star"></i> CYBER NINJA MASTER
                        {% elif total_points >= 500 %}
                        <i class="fa fa-bolt"></i> DIGITAL WARRIOR
                        {% elif total_points >= 200 %}
                        <i class="fa fa-laptop"></i> CODE RUNNER
                        {% else %}
                        <i class="fa fa-leaf"></i> NET ROOKIE
                        {% endif %}
                    </h4>
                </div>

                <div class="w3-row-padding w3-margin-top">
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card cyber-card w3-center w3-padding">
                            <i class="fa fa-star text-cyber-green w3-large cyber-glow"></i>
                            <h4 class="text-cyber-cyan" style="text-transform: uppercase; letter-spacing: 1px;">STAR HACKER</h4>
                            <p class="text-cyber-pink">{{ stats.total_stars }} stars</p>
                        </div>
                    </div>
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card cyber-card w3-center w3-padding">
                            <i class="fa fa-code-fork text-cyber-cyan w3-large cyber-glow"></i>
                            <h4 class="text-cyber-cyan" style="text-transform: uppercase; letter-spacing: 1px;">FORK MASTER</h4>
                            <p class="text-cyber-pink">{{ stats.total_forks }} forks</p>
                        </div>
                    </div>
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card cyber-card w3-center w3-padding">
                            <i class="fa fa-book text-cyber-pink w3-large cyber-glow"></i>
                            <h4 class="text-cyber-cyan" style="text-transform: uppercase; letter-spacing: 1px;">POLYGLOT CODER</h4>
                            <p class="text-cyber-pink">{{ stats.languages|length }} linguagens</p>
                        </div>
                    </div>
                    <div class="w3-col s12 m6 l3">
                        <div class="w3-card cyber-card w3-center w3-padding">
                            <i class="fa fa-rocket text-cyber-green w3-large cyber-glow"></i>
                            <h4 class="text-cyber-cyan" style="text-transform: uppercase; letter-spacing: 1px;">PROJECT ARCHITECT</h4>
                            <p class="text-cyber-pink">{{ stats.total_repos }} repositórios</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Footer -->
            <footer class="w3-container w3-center w3-padding-32 w3-border-top w3-border-pink">
                <p class="text-cyber-cyan cyber-glow" style="text-transform: uppercase; letter-spacing: 2px;">
                    <i class="fa fa-code"></i> CURRÍCULO GERADO PELO GITHUB ATS RESUME GENERATOR
                </p>
            </footer>
        </div>
    </div>
</body>
</html>
