<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>urrí<PERSON>lo Cyberpunk</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Orbitron', monospace;
            line-height: 1.6;
            color: #00ff41;
            background: #0a0a0a;
            padding: 20px;
            background-image: 
                radial-gradient(circle at 25% 25%, #ff00ff 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #00ffff 0%, transparent 50%);
            background-size: 100px 100px;
            animation: backgroundShift 10s ease-in-out infinite alternate;
        }
        
        @keyframes backgroundShift {
            0% { background-position: 0% 0%; }
            100% { background-position: 100% 100%; }
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.9);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 
                0 0 50px rgba(0, 255, 65, 0.3),
                inset 0 0 50px rgba(255, 0, 255, 0.1);
            border: 2px solid #00ff41;
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.1), transparent);
            animation: scan 3s linear infinite;
        }
        
        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #ff00ff;
            padding-bottom: 20px;
            margin-bottom: 30px;
            position: relative;
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: 900;
            color: #00ff41;
            margin-bottom: 10px;
            text-shadow: 
                0 0 10px #00ff41,
                0 0 20px #00ff41,
                0 0 30px #00ff41;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 10px #00ff41, 0 0 20px #00ff41, 0 0 30px #00ff41; }
            to { text-shadow: 0 0 20px #00ff41, 0 0 30px #00ff41, 0 0 40px #00ff41; }
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #00ffff;
            text-shadow: 0 0 5px #00ffff;
        }
        
        .section {
            margin-bottom: 30px;
            position: relative;
        }
        
        .section h2 {
            color: #ff00ff;
            border-bottom: 2px solid #ff00ff;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.5em;
            text-shadow: 0 0 10px #ff00ff;
            font-weight: 700;
        }
        
        .professional-summary {
            background: rgba(255, 0, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ff00ff;
            border-left: 4px solid #ff00ff;
            font-style: italic;
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.2);
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .skill-item {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            padding: 8px 12px;
            border-radius: 20px;
            text-align: center;
            font-weight: 700;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 0 15px rgba(255, 0, 255, 0.5);
            transition: all 0.3s;
        }
        
        .skill-item:hover {
            transform: scale(1.05);
            box-shadow: 0 0 25px rgba(0, 255, 255, 0.8);
        }
        
        .project {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff41;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .project::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #ff00ff, #00ffff, #00ff41);
            animation: borderGlow 2s linear infinite;
        }
        
        @keyframes borderGlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .project:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 10px 30px rgba(0, 255, 65, 0.3),
                0 0 50px rgba(255, 0, 255, 0.2);
            border-color: #ff00ff;
        }
        
        .project h3 {
            color: #00ff41;
            margin-bottom: 10px;
            text-shadow: 0 0 5px #00ff41;
            font-weight: 700;
        }
        
        .project-meta {
            display: flex;
            gap: 15px;
            margin: 10px 0;
            font-size: 0.9em;
            color: #00ffff;
        }
        
        .project-link {
            display: inline-block;
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            color: #000;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 25px;
            margin-top: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.5);
        }
        
        .project-link:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255, 0, 255, 0.2), rgba(0, 255, 255, 0.2));
            color: #00ff41;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #00ff41;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: 900;
            display: block;
            color: #ff00ff;
            text-shadow: 0 0 10px #ff00ff;
        }
        
        .languages-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .language-tag {
            background: linear-gradient(135deg, #00ff41, #00ffff);
            color: #000;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 0 15px rgba(0, 255, 65, 0.5);
        }
        
        .gamification {
            background: linear-gradient(135deg, rgba(255, 0, 255, 0.2), rgba(0, 255, 255, 0.2));
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #ff00ff;
            box-shadow: 
                0 0 30px rgba(255, 0, 255, 0.5),
                inset 0 0 30px rgba(0, 255, 255, 0.1);
            position: relative;
        }
        
        .level-badge {
            font-size: 2em;
            margin: 15px 0;
            color: #00ff41;
            text-shadow: 0 0 15px #00ff41;
            font-weight: 900;
        }
        
        .badges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .badge-item {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            transition: all 0.3s;
        }
        
        .badge-item:hover {
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.6);
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #ff00ff;
            color: #00ffff;
            font-style: italic;
            text-shadow: 0 0 5px #00ffff;
        }
        
        @media print {
            body { background: white; color: black; }
            .container { background: white; color: black; box-shadow: none; }
            * { text-shadow: none !important; box-shadow: none !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Douglas H. Machado</h1>
            <div class="contact-info">
                
                <div class="contact-item">📍 Rio de Janeiro</div>
                <div class="contact-item">🌐 https://asimov-tech-institucional.vercel.app/</div>
                <div class="contact-item">🏢 ASIMOV TECH</div>
            </div>
        </header>

        <section class="section">
            <h2>👨‍💻 PERFIL PROFISSIONAL</h2>
            <div class="professional-summary">
                Como resumo profissional otimizado para ATS, destaco a sólida experiência técnica de Douglas H. Machado, um CTO visionário na ASIMOV TECH, com especialização em Engenharia de Dados e proficiência em Machine Learning, Big Data e Blockchain. Com residência no vibrante ecossistema tecnológico do Rio de Janeiro, Douglas é um especialista em Python, complementado por seu domínio em TypeScript, JavaScript, HTML e CSS.

Sua presença influente no GitHub é evidenciada por 532 repositórios públicos, que refletem sua dedicação à inovação aberta e colaborativa. Com um impressionante total de 35,025 estrelas, seus projetos demonstram um impacto significativo e reconhecimento na comunidade de desenvolvedores. Sua base de seguidores de 139 pessoas é um testemunho de sua liderança e contribuições valiosas para o campo.

Douglas é um arquiteto de soluções que combina sua paixão por tecnologia com uma abordagem sistemática, possuindo um histórico comprovado de entrega de projetos de alta complexidade. Su
            </div>
        </section>

        <section class="section">
            <h2>🛠️ HABILIDADES TÉCNICAS</h2>
            <div class="skills-grid">
                
                
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                      
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                      
                    
                  
                
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                  
                    
                  
                
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                    
                  
                
                  
                    
                  
                    
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                
                  
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                      
                    
                  
                    
                  
                
                
                <div class="skill-item">TypeScript</div>
                
                <div class="skill-item">JavaScript</div>
                
                <div class="skill-item">Shell Scripting</div>
                
                <div class="skill-item">CSS</div>
                
                <div class="skill-item">Docker</div>
                
                <div class="skill-item">Docker Compose</div>
                
                <div class="skill-item">Node.js</div>
                
                <div class="skill-item">PostgreSQL</div>
                
                <div class="skill-item">Desenvolvimento Web</div>
                
                <div class="skill-item">Desenvolvimento de Interfaces de Usuário</div>
                
                <div class="skill-item">Administração de Sistemas</div>
                
                <div class="skill-item">Segurança de Redes</div>
                
                <div class="skill-item">Desenvolvimento de Engines de Jogo</div>
                
                <div class="skill-item">C++</div>
                
                <div class="skill-item">C#</div>
                
            </div>
        </section>

        <section class="section">
            <h2>🚀 PROJETOS EM DESTAQUE</h2>
            
            <div class="project">
                <h3>Desenvolvedor Full Stack - Projeto Sem Fronteiras SSH</h3>
                
                <p>Criador de um sistema web avançado para gerenciamento centralizado de servidores remotos via SSH/RDP, com interface intuitiva e segura.</p>
                
                
                <div class="project-meta">
                    <span><strong>TECH:</strong> TypeScript, JavaScript, Shell Scripting, CSS, Docker, Docker Compose, Node.js, PostgreSQL, Desenvolvimento Web, Desenvolvimento de Interfaces de Usuário, Administração de Sistemas, Segurança de Redes</span>
                    
                    
                </div>
                
                
                <div>
                    <strong>CONQUISTAS:</strong>
                    <ul>
                    
                        <li>Desenvolvimento de um sistema de gerenciamento SSH/RDP de ponta a ponta</li>
                    
                        <li>Implementação de uma interface de usuário intuitiva para administração de servidores remotos</li>
                    
                        <li>Integração de Docker para facilitar o setup e a manutenção do ambiente de desenvolvimento e produção</li>
                    
                        <li>Criação de documentação técnica abrangente, incluindo especificações do projeto e estrutura do banco de dados</li>
                    
                    </ul>
                </div>
                
                
                <a href="https://github.com/rafaelsantanna/sem-fronteiras-ssh" class="project-link" target="_blank">ACESSAR</a>
            </div>
            
            <div class="project">
                <h3>Unreal Engine - Motor de Jogo C++ de Alto Desempenho</h3>
                
                <p>Desenvolvimento colaborativo do código-fonte do Unreal Engine, um motor de jogo líder com suporte a C++, C#, e Python, destacando-se na criação de experiências visuais imersivas e jogabilidade avançada.</p>
                
                
                <div class="project-meta">
                    <span><strong>TECH:</strong> Desenvolvimento de Engines de Jogo, C++, C#, Python, HLSL (High-Level Shading Language), Compilação Multiplataforma, Otimização de Desempenho, Integração de APIs, Versionamento de Código com Git</span>
                    <span>⭐ 31271</span>
                    <span>🍴 55143</span>
                </div>
                
                
                <div>
                    <strong>CONQUISTAS:</strong>
                    <ul>
                    
                        <li>Mais de 31.000 estrelas no GitHub</li>
                    
                        <li>Superior a 55.000 forks, indicando ampla adoção e contribuição da comunidade</li>
                    
                        <li>Suporte para compilação do Unreal Editor em Windows, Mac e Linux</li>
                    
                        <li>Capacidade de compilar jogos para diversas plataformas de destino</li>
                    
                    </ul>
                </div>
                
                
                <a href="https://github.com/EpicGames/UnrealEngine" class="project-link" target="_blank">ACESSAR</a>
            </div>
            
            <div class="project">
                <h3>UGCExample: Mod Support Integration for UE4</h3>
                
                <p>Desenvolvimento de um plugin e scripts de construção para adicionar suporte a mods em jogos Unreal Engine 4, inspirado na implementação de Robo Recall.</p>
                
                
                <div class="project-meta">
                    <span><strong>TECH:</strong> Desenvolvimento em C++, Plugin Development for Unreal Engine 4, Cross-platform Development (C#, C), Automation Tool Scripting, Game Development, User-Generated Content Management</span>
                    <span>⭐ 405</span>
                    <span>🍴 160</span>
                </div>
                
                
                <div>
                    <strong>CONQUISTAS:</strong>
                    <ul>
                    
                        <li>405 estrelas no GitHub</li>
                    
                        <li>160 forks indicando adoção e interesse da comunidade</li>
                    
                        <li>Inclusão de módulos de runtime e editor para integração de jogos</li>
                    
                        <li>Criação de um projeto de exemplo completo demonstrando a integração de mods</li>
                    
                    </ul>
                </div>
                
                
                <a href="https://github.com/EpicGames/UGCExample" class="project-link" target="_blank">ACESSAR</a>
            </div>
            
            <div class="project">
                <h3>Engenheiro de Software Full Stack - AIM Engenharia</h3>
                
                <p>Desenvolvedor Full Stack com experiência em TypeScript, React, NestJS e Docker, especializado em criar soluções robustas para automação na construção civil. Implementou um MVP para gestão de cronogramas e orçamentos com autenticação Google SSO e RBAC.</p>
                
                
                <div class="project-meta">
                    <span><strong>TECH:</strong> TypeScript, React, NestJS, Docker, Python, FastAPI, CSS, Google SSO, RBAC, RPA, Parser de arquivos .mpp, PDF e Excel, Automação de processos</span>
                    
                    
                </div>
                
                
                <div>
                    <strong>CONQUISTAS:</strong>
                    <ul>
                    
                        <li>Configuração de autenticação Google SSO para plataforma de automação</li>
                    
                        <li>Implementação de RBAC para controle de acesso baseado em funções</li>
                    
                        <li>Desenvolvimento de sistemas de upload de arquivos MPP, PDF e Excel</li>
                    
                        <li>Criação de interface para visualização de cronogramas e orçamentos</li>
                    
                        <li>Integração de dashboard com métricas de desempenho</li>
                    
                    </ul>
                </div>
                
                
                <a href="https://github.com/dougdotcon/AIMEngenharia" class="project-link" target="_blank">ACESSAR</a>
            </div>
            
            <div class="project">
                <h3>Desenvolvedor de Sistema de Dublagem Automática com TTS</h3>
                
                <p>Engenheiro de software especializado na criação de um sistema de dublagem automática para vídeos do YouTube, utilizando tecnologias de conversão de texto em fala (TTS) e processamento de vídeo com FFmpeg, otimizado para Python 3.8+ e Windows 11.</p>
                
                
                <div class="project-meta">
                    <span><strong>TECH:</strong> Desenvolvimento Python, Processamento de Áudio e Vídeo com FFmpeg, Integração de APIs de TTS (Text-to-Speech), Automação de fluxos de trabalho de dublagem, Desenvolvimento de sistemas de conversão de linguagem</span>
                    
                    
                </div>
                
                
                <div>
                    <strong>CONQUISTAS:</strong>
                    <ul>
                    
                        <li>Desenvolvimento de um sistema completo para dublagem automática</li>
                    
                        <li>Implementação de uma solução TTS local para conversão de conteúdo em inglês para português brasileiro</li>
                    
                        <li>Otimização do sistema para execução em ambientes Windows 11</li>
                    
                        <li>Criação de interfaces de usuário para facilitar o processo de dublagem</li>
                    
                    </ul>
                </div>
                
                
                <a href="https://github.com/dougdotcon/DublagemBR" class="project-link" target="_blank">ACESSAR</a>
            </div>
            
            <div class="project">
                <h3>DevHubs: Plataforma de Aprendizado Prático para Desenvolvedores</h3>
                
                <p>Desenvolvedor de uma plataforma inovadora que integra micro-tarefas, mentoria orientada por LLMs e elementos de gamificação para melhorar as habilidades de codificação.</p>
                
                
                <div class="project-meta">
                    <span><strong>TECH:</strong> Python, Django, Django REST Framework, PostgreSQL, Redis, Celery, Django Channels, WebSockets, GitHub Actions, Docker, Gamificação, Machine Learning Models (LLMs)</span>
                    
                    
                </div>
                
                
                <div>
                    <strong>CONQUISTAS:</strong>
                    <ul>
                    
                        <li>Desenvolvimento de um sistema de quests para desafios de codificação</li>
                    
                        <li>Integração de mentoria automatizada com IA para orientação de tarefas</li>
                    
                        <li>Implementação de um sistema de recompensas baseado em experiência e badges</li>
                    
                        <li>Criação de uma plataforma de aprendizado hands-on guiado por IA</li>
                    
                    </ul>
                </div>
                
                
                <a href="https://github.com/dougdotcon/devhubs" class="project-link" target="_blank">ACESSAR</a>
            </div>
            
        </section>

        <section class="section">
            <h2>📊 ESTATÍSTICAS GITHUB</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">532</span>
                    <span>REPOS</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">35025</span>
                    <span>STARS</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">58285</span>
                    <span>FORKS</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">139</span>
                    <span>FOLLOWERS</span>
                </div>
            </div>
            
            <h3>💻 LINGUAGENS</h3>
            <div class="languages-list">
                
                <div class="language-tag">Python (128)</div>
                
                <div class="language-tag">TypeScript (90)</div>
                
                <div class="language-tag">JavaScript (89)</div>
                
                <div class="language-tag">HTML (42)</div>
                
                <div class="language-tag">CSS (23)</div>
                
                <div class="language-tag">Jupyter Notebook (10)</div>
                
                <div class="language-tag">PHP (8)</div>
                
                <div class="language-tag">C++ (7)</div>
                
                <div class="language-tag">Java (7)</div>
                
                <div class="language-tag">Go (7)</div>
                
            </div>
        </section>

        
        <section class="section">
            <h2>🎮 CONQUISTAS CYBERPUNK</h2>
            <div class="gamification">
                
                
                <div class="level-badge">
                    🎯 642889 PONTOS
                </div>
                
                <div class="level-badge">
                    
                    🏅 🚀 CYBER NINJA MASTER
                    
                </div>
                
                <div class="badges-grid">
                    <div class="badge-item">
                        <div>🌟 <strong>STAR HACKER</strong></div>
                        <div>35025 stars</div>
                    </div>
                    <div class="badge-item">
                        <div>🍴 <strong>FORK MASTER</strong></div>
                        <div>58285 forks</div>
                    </div>
                    <div class="badge-item">
                        <div>📚 <strong>POLYGLOT CODER</strong></div>
                        <div>10 linguagens</div>
                    </div>
                    <div class="badge-item">
                        <div>🚀 <strong>PROJECT ARCHITECT</strong></div>
                        <div>532 repositórios</div>
                    </div>
                </div>
            </div>
        </section>
        

        <footer class="footer">
            <p>>>> CURRÍCULO GERADO PELO GITHUB ATS RESUME GENERATOR 🚀 <<<</p>
        </footer>
    </div>
</body>
</html>